#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OpenRouter API积分查询工具
支持多key轮询和socks5代理功能
Author: Chief Programmer
修复版本：正确解析OpenRouter API响应格式
"""

import requests
import json
import time
import logging
from typing import List, Dict, Optional, Tuple, Union
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor, as_completed
import argparse
import sys
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('openrouter_credits.log')
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class ProxyConfig:
    """代理配置类"""
    socks5_host: str = "127.0.0.1"
    socks5_port: int = 1080
    username: Optional[str] = None
    password: Optional[str] = None
    
    def get_proxy_dict(self) -> Dict[str, str]:
        """获取代理配置字典"""
        if self.username and self.password:
            proxy_url = f"socks5://{self.username}:{self.password}@{self.socks5_host}:{self.socks5_port}"
        else:
            proxy_url = f"socks5://{self.socks5_host}:{self.socks5_port}"
        
        return {
            'http': proxy_url,
            'https': proxy_url
        }

@dataclass
class APIKeyInfo:
    """API Key信息类"""
    key: str
    name: str = ""
    credits: Optional[float] = None
    usage: Optional[Dict] = None
    status: str = "unknown"
    error: Optional[str] = None
    response_time: Optional[float] = None

class OpenRouterCreditsChecker:
    """OpenRouter积分查询器"""
    
    BASE_URL = "https://openrouter.ai/api/v1/credits"
    
    def __init__(self, api_keys: List[str], proxy_config: Optional[ProxyConfig] = None):
        """
        初始化查询器
        
        Args:
            api_keys: API key列表
            proxy_config: 代理配置
        """
        self.api_keys = [APIKeyInfo(key=key, name=f"Key-{i+1}") for i, key in enumerate(api_keys)]
        self.proxy_config = proxy_config
        self.session = self._create_session()
    
    def _create_session(self) -> requests.Session:
        """创建请求会话"""
        session = requests.Session()
        
        # 设置通用请求头
        session.headers.update({
            'User-Agent': 'OpenRouter-Credits-Checker/1.0',
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        })
        
        # 配置代理
        if self.proxy_config:
            try:
                session.proxies.update(self.proxy_config.get_proxy_dict())
                logger.info(f"已配置SOCKS5代理: {self.proxy_config.socks5_host}:{self.proxy_config.socks5_port}")
            except Exception as e:
                logger.warning(f"代理配置失败: {e}")
        
        return session
    
    def _extract_credits_from_response(self, response_data: Dict) -> float:
        """
        从API响应中提取积分信息
        
        Args:
            response_data: API响应数据
            
        Returns:
            剩余积分数量（浮点数）
        """
        try:
            # 优先从data字段获取
            if 'data' in response_data:
                data = response_data['data']
                
                # 如果data直接是数字
                if isinstance(data, (int, float)):
                    return float(data)
                
                # 如果data是字典，查找可能的字段
                if isinstance(data, dict):
                    # 🔥 修复：正确处理OpenRouter的返回格式
                    if 'total_credits' in data and 'total_usage' in data:
                        total_credits = float(data.get('total_credits', 0))
                        total_usage = float(data.get('total_usage', 0))
                        remaining = total_credits - total_usage
                        logger.info(f"积分详情 - 总购买: ${total_credits:.4f}, 已使用: ${total_usage:.4f}, 剩余: ${remaining:.4f}")
                        return remaining
                    
                    # 兼容其他可能的格式
                    if 'purchased' in data and 'used' in data:
                        purchased = float(data.get('purchased', 0))
                        used = float(data.get('used', 0))
                        return purchased - used
                    
                    # 常见的直接积分字段名
                    credit_fields = ['credits', 'balance', 'total', 'amount', 'value', 'remaining']
                    for field in credit_fields:
                        if field in data and isinstance(data[field], (int, float)):
                            return float(data[field])
                    
                    # 如果找不到明确的积分字段，记录详细信息用于调试
                    logger.warning(f"未识别的data结构: {data}")
                    return 0.0
            
            # 如果data字段不存在，直接查找根级别字段
            credit_fields = ['credits', 'balance', 'total', 'amount', 'purchased']
            for field in credit_fields:
                if field in response_data and isinstance(response_data[field], (int, float)):
                    return float(response_data[field])
            
            # 都找不到的话，记录完整响应并返回0
            logger.warning(f"无法从响应中提取积分信息: {response_data}")
            return 0.0
            
        except Exception as e:
            logger.error(f"解析积分数据时发生错误: {e}")
            return 0.0
    
    def check_single_key(self, api_key_info: APIKeyInfo, timeout: int = 30) -> APIKeyInfo:
        """
        检查单个API key的积分
        
        Args:
            api_key_info: API key信息
            timeout: 请求超时时间
            
        Returns:
            更新后的API key信息
        """
        headers = {
            'Authorization': f'Bearer {api_key_info.key}',
        }
        
        start_time = time.time()
        
        try:
            logger.info(f"正在查询 {api_key_info.name}...")
            
            response = self.session.get(
                self.BASE_URL,
                headers=headers,
                timeout=timeout
            )
            
            api_key_info.response_time = time.time() - start_time
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    
                    # 使用新的积分提取方法
                    api_key_info.credits = self._extract_credits_from_response(data)
                    
                    # 保存完整的使用信息（如果存在）
                    if 'data' in data and isinstance(data['data'], dict):
                        api_key_info.usage = data['data']
                    else:
                        api_key_info.usage = data
                    
                    api_key_info.status = "success"
                    logger.info(f"{api_key_info.name} 查询成功，剩余积分: ${api_key_info.credits:.4f}")
                    
                except json.JSONDecodeError as e:
                    api_key_info.status = "parse_error"
                    api_key_info.error = f"JSON解析失败: {str(e)}"
                    logger.error(f"{api_key_info.name} - JSON解析失败: {e}")
                    
            elif response.status_code == 401:
                api_key_info.status = "invalid"
                api_key_info.error = "API Key无效或已过期"
                logger.warning(f"{api_key_info.name} - API Key无效")
                
            elif response.status_code == 429:
                api_key_info.status = "rate_limited"
                api_key_info.error = "请求频率超限"
                logger.warning(f"{api_key_info.name} - 请求频率超限")
                
            else:
                api_key_info.status = "error"
                api_key_info.error = f"HTTP {response.status_code}: {response.text[:200]}"
                logger.error(f"{api_key_info.name} - 请求失败: {response.status_code}")
                
        except requests.exceptions.Timeout:
            api_key_info.status = "timeout"
            api_key_info.error = f"请求超时({timeout}秒)"
            api_key_info.response_time = time.time() - start_time
            logger.error(f"{api_key_info.name} - 请求超时")
            
        except requests.exceptions.ProxyError:
            api_key_info.status = "proxy_error"
            api_key_info.error = "代理连接失败"
            logger.error(f"{api_key_info.name} - 代理连接失败")
            
        except requests.exceptions.RequestException as e:
            api_key_info.status = "network_error"
            api_key_info.error = f"网络错误: {str(e)}"
            logger.error(f"{api_key_info.name} - 网络错误: {e}")
            
        except Exception as e:
            api_key_info.status = "unknown_error"
            api_key_info.error = f"未知错误: {str(e)}"
            logger.error(f"{api_key_info.name} - 未知错误: {e}")
            
        return api_key_info
    
    def check_all_keys_sequential(self, delay: float = 1.0, timeout: int = 30) -> List[APIKeyInfo]:
        """
        顺序查询所有API key
        
        Args:
            delay: 请求间隔时间（秒）
            timeout: 请求超时时间
            
        Returns:
            所有API key的查询结果
        """
        logger.info(f"开始顺序查询 {len(self.api_keys)} 个API Key...")
        
        for i, api_key_info in enumerate(self.api_keys):
            self.check_single_key(api_key_info, timeout)
            
            # 添加延迟，避免频率限制
            if i < len(self.api_keys) - 1 and delay > 0:
                time.sleep(delay)
        
        return self.api_keys
    
    def check_all_keys_concurrent(self, max_workers: int = 5, timeout: int = 30) -> List[APIKeyInfo]:
        """
        并发查询所有API key
        
        Args:
            max_workers: 最大并发数
            timeout: 请求超时时间
            
        Returns:
            所有API key的查询结果
        """
        logger.info(f"开始并发查询 {len(self.api_keys)} 个API Key (最大并发: {max_workers})...")
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_key = {
                executor.submit(self.check_single_key, api_key_info, timeout): api_key_info
                for api_key_info in self.api_keys
            }
            
            for future in as_completed(future_to_key):
                api_key_info = future_to_key[future]
                try:
                    future.result()
                except Exception as e:
                    logger.error(f"{api_key_info.name} 并发查询异常: {e}")
        
        return self.api_keys
    
    def print_results(self, show_details: bool = True):
        """打印查询结果"""
        print("\n" + "="*80)
        print("OpenRouter API积分查询结果")
        print("="*80)
        
        successful_keys = [key for key in self.api_keys if key.status == "success"]
        
        # 修复：安全地计算总积分，确保只对数字进行求和
        total_credits = 0.0
        for key in successful_keys:
            if key.credits is not None and isinstance(key.credits, (int, float)):
                total_credits += float(key.credits)
        
        print(f"查询统计:")
        print(f"  总计: {len(self.api_keys)} 个Key")
        print(f"  成功: {len(successful_keys)} 个")
        print(f"  失败: {len(self.api_keys) - len(successful_keys)} 个")
        print(f"  总剩余积分: ${total_credits:.4f}")
        print()
        
        # 详细结果
        for api_key_info in self.api_keys:
            status_icon = {
                "success": "✅",
                "invalid": "❌",
                "rate_limited": "⚠️",
                "timeout": "⏰",
                "proxy_error": "🔒",
                "network_error": "🌐",
                "parse_error": "🔍",
                "error": "❌",
                "unknown_error": "❓",
                "unknown": "⭕"
            }.get(api_key_info.status, "❓")
            
            print(f"{status_icon} {api_key_info.name}")
            print(f"  Key: {api_key_info.key[:8]}...{api_key_info.key[-8:]}")
            
            if api_key_info.status == "success":
                print(f"  剩余积分: ${api_key_info.credits:.4f}")
                if api_key_info.response_time:
                    print(f"  响应时间: {api_key_info.response_time:.2f}s")
                    
                if show_details and api_key_info.usage:
                    usage_data = api_key_info.usage.copy()
                    # 添加计算出的剩余积分到详情中
                    if 'total_credits' in usage_data and 'total_usage' in usage_data:
                        usage_data['remaining_credits'] = usage_data['total_credits'] - usage_data['total_usage']
                    print(f"  详细信息: {json.dumps(usage_data, indent=4, ensure_ascii=False)}")
            else:
                print(f"  状态: {api_key_info.error}")
            print()
    
    def export_to_json(self, filename: str):
        """导出结果到JSON文件"""
        report_data = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "total_keys": len(self.api_keys),
            "successful_keys": len([k for k in self.api_keys if k.status == "success"]),
            "total_credits": sum(
                float(key.credits) for key in self.api_keys 
                if key.status == "success" and key.credits is not None
            ),
            "keys": [
                {
                    "name": key.name,
                    "key_preview": f"{key.key[:8]}...{key.key[-8:]}",
                    "status": key.status,
                    "credits": key.credits,
                    "usage": key.usage,
                    "error": key.error,
                    "response_time": key.response_time
                }
                for key in self.api_keys
            ]
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"查询结果已导出到: {filename}")

def load_keys_from_file(filename: str) -> List[str]:
    """从文件加载API keys"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            keys = [line.strip() for line in f if line.strip() and not line.startswith('#')]
        logger.info(f"从 {filename} 加载了 {len(keys)} 个API Key")
        return keys
    except FileNotFoundError:
        logger.error(f"文件不存在: {filename}")
        return []
    except Exception as e:
        logger.error(f"读取文件失败: {e}")
        return []

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="OpenRouter API积分查询工具")
    parser.add_argument("--keys", "-k", nargs="+", help="API Keys (直接输入)")
    parser.add_argument("--file", "-f", help="包含API Keys的文件路径")
    parser.add_argument("--proxy-host", default="127.0.0.1", help="SOCKS5代理主机 (默认: 127.0.0.1)")
    parser.add_argument("--proxy-port", type=int, default=1080, help="SOCKS5代理端口 (默认: 1080)")
    parser.add_argument("--proxy-user", help="代理用户名")
    parser.add_argument("--proxy-pass", help="代理密码")
    parser.add_argument("--no-proxy", action="store_true", help="不使用代理")
    parser.add_argument("--concurrent", "-c", action="store_true", help="并发查询 (默认: 顺序查询)")
    parser.add_argument("--max-workers", type=int, default=5, help="最大并发数 (默认: 5)")
    parser.add_argument("--delay", type=float, default=1.0, help="顺序查询时的延迟时间/秒 (默认: 1.0)")
    parser.add_argument("--timeout", type=int, default=30, help="请求超时时间/秒 (默认: 30)")
    parser.add_argument("--export", help="导出结果到JSON文件")
    parser.add_argument("--simple", action="store_true", help="简化输出，不显示详细信息")
    parser.add_argument("--debug", action="store_true", help="启用调试模式，显示详细的API响应")
    
    args = parser.parse_args()
    
    # 启用调试模式
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
        logger.debug("已启用调试模式")
    
    # 获取API Keys
    api_keys = []
    if args.keys:
        api_keys.extend(args.keys)
    if args.file:
        api_keys.extend(load_keys_from_file(args.file))
    
    if not api_keys:
        logger.error("未提供任何API Key，请使用 --keys 或 --file 参数")
        print("\n使用示例:")
        print("  python openrouter.py --keys sk-or-v1-xxx sk-or-v1-yyy")
        print("  python openrouter.py --file keys.txt")
        print("  python openrouter.py --file keys.txt --proxy-host 127.0.0.1 --proxy-port 3080")
        print("  python openrouter.py --file keys.txt --debug  # 调试模式")
        print("  python openrouter.py --file keys.txt --concurrent --max-workers 10  # 并发查询")
        print("  python openrouter.py --file keys.txt --export results.json  # 导出结果")
        sys.exit(1)
    
    # 配置代理
    proxy_config = None
    if not args.no_proxy:
        proxy_config = ProxyConfig(
            socks5_host=args.proxy_host,
            socks5_port=args.proxy_port,
            username=args.proxy_user,
            password=args.proxy_pass
        )
    
    # 创建查询器
    checker = OpenRouterCreditsChecker(api_keys, proxy_config)
    
    try:
        # 执行查询
        start_time = time.time()
        if args.concurrent:
            checker.check_all_keys_concurrent(args.max_workers, args.timeout)
        else:
            checker.check_all_keys_sequential(args.delay, args.timeout)
        
        total_time = time.time() - start_time
        logger.info(f"查询完成，总用时: {total_time:.2f}秒")
        
        # 显示结果
        checker.print_results(show_details=not args.simple)
        
        # 导出结果
        if args.export:
            checker.export_to_json(args.export)
        
    except KeyboardInterrupt:
        logger.info("用户中断查询")
    except Exception as e:
        logger.error(f"查询过程中发生错误: {e}")
        if args.debug:
            import traceback
            logger.debug(f"详细错误信息:\n{traceback.format_exc()}")
        sys.exit(1)

def create_sample_config():
    """创建示例配置文件"""
    sample_keys = [
        "# OpenRouter API Keys配置文件",
        "# 每行一个API Key，以#开头的行为注释",
        "# 请替换为您的真实API Key",
        "sk-or-v1-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
        "sk-or-v1-yyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyy",
        "sk-or-v1-zzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzz"
    ]
    
    with open("keys_sample.txt", "w", encoding="utf-8") as f:
        f.write("\n".join(sample_keys))
    
    print("已创建示例配置文件: keys_sample.txt")
    print("请编辑此文件，替换为您的真实API Key")

def test_api_response():
    """测试API响应格式的工具函数"""
    print("OpenRouter Credits API响应格式测试工具")
    print("请提供一个有效的API Key进行测试...")
    
    api_key = input("请输入API Key: ").strip()
    if not api_key:
        print("未提供API Key，退出测试")
        return
    
    # 询问是否使用代理
    use_proxy = input("是否使用代理？(y/N): ").strip().lower()
    proxy_config = None
    
    if use_proxy == 'y':
        proxy_host = input("代理主机 (默认: 127.0.0.1): ").strip() or "127.0.0.1"
        proxy_port = input("代理端口 (默认: 1080): ").strip() or "1080"
        
        try:
            proxy_port = int(proxy_port)
            proxy_config = ProxyConfig(socks5_host=proxy_host, socks5_port=proxy_port)
            print(f"将使用代理: {proxy_host}:{proxy_port}")
        except ValueError:
            print("端口号格式错误，不使用代理")
    
    checker = OpenRouterCreditsChecker([api_key], proxy_config)
    
    # 测试单个key
    api_key_info = checker.api_keys[0]
    api_key_info.name = "测试Key"
    
    print("\n开始测试API响应...")
    result = checker.check_single_key(api_key_info)
    
    print(f"\n{'='*50}")
    print(f"测试结果:")
    print(f"{'='*50}")
    print(f"状态: {result.status}")
    print(f"剩余积分: ${result.credits:.4f}" if result.credits is not None else "积分: 获取失败")
    print(f"响应时间: {result.response_time:.2f}s" if result.response_time else "响应时间: 未知")
    
    if result.error:
        print(f"错误: {result.error}")
    
    if result.usage:
        print(f"完整响应数据:")
        print(json.dumps(result.usage, indent=2, ensure_ascii=False))

def show_usage_examples():
    """显示使用示例"""
    examples = [
        {
            "description": "基础使用 - 从文件读取keys",
            "command": "python openrouter.py --file keys.txt"
        },
        {
            "description": "使用代理查询",
            "command": "python openrouter.py --file keys.txt --proxy-host 127.0.0.1 --proxy-port 3080"
        },
        {
            "description": "直接输入API Keys",
            "command": "python openrouter.py --keys sk-or-v1-xxx sk-or-v1-yyy"
        },
        {
            "description": "并发查询（更快）",
            "command": "python openrouter.py --file keys.txt --concurrent --max-workers 10"
        },
        {
            "description": "调试模式（查看详细日志）",
            "command": "python openrouter.py --file keys.txt --debug"
        },
        {
            "description": "导出结果到JSON文件",
            "command": "python openrouter.py --file keys.txt --export results.json"
        },
        {
            "description": "简化输出（不显示详细信息）",
            "command": "python openrouter.py --file keys.txt --simple"
        },
        {
            "description": "自定义延迟和超时",
            "command": "python openrouter.py --file keys.txt --delay 0.5 --timeout 60"
        },
        {
            "description": "不使用代理",
            "command": "python openrouter.py --file keys.txt --no-proxy"
        }
    ]
    
    print("OpenRouter API积分查询工具 - 使用示例")
    print("="*80)
    
    for i, example in enumerate(examples, 1):
        print(f"\n{i}. {example['description']}")
        print(f"   {example['command']}")
    
    print(f"\n{'='*80}")
    print("辅助工具:")
    print("  python openrouter.py --create-sample  # 创建示例keys文件")
    print("  python openrouter.py --test-api       # 测试单个API Key")
    print("  python openrouter.py --help           # 显示详细帮助")

if __name__ == "__main__":
    # 检查是否需要安装依赖
    try:
        import requests
    except ImportError:
        print("错误: 缺少requests库")
        print("请先安装依赖: pip install requests requests[socks]")
        sys.exit(1)
    
    # 特殊命令处理
    if len(sys.argv) > 1:
        if sys.argv[1] == "--create-sample":
            create_sample_config()
            sys.exit(0)
        elif sys.argv[1] == "--test-api":
            test_api_response()
            sys.exit(0)
        elif sys.argv[1] == "--examples":
            show_usage_examples()
            sys.exit(0)
    
    # 如果没有参数，显示基本帮助
    if len(sys.argv) == 1:
        print("OpenRouter API积分查询工具")
        print("请使用 --help 查看详细帮助，或使用 --examples 查看使用示例")
        sys.exit(0)
    
    main()
